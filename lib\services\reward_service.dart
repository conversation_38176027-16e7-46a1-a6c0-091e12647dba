import 'package:bibl/controllers/profile_controller.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../widgets/achivement_dialog.dart';
import 'leaderboard_service.dart';

class RewardService {
  final String uid; // pass the user UID here
  final FirebaseFirestore firestore = FirebaseFirestore.instance;

  RewardService(this.uid);

  /// Helper method to check if achievement is already earned
  bool _hasAchievement(String achievementId) {
    final ProfileController profileController = Get.find();
    final achievements = profileController.userr.value.achievements ?? [];
    final hasIt = achievements.contains(achievementId);
    debugPrint(
        '_hasAchievement($achievementId): $hasIt, achievements: $achievements');
    // Always use fresh data from profile controller to avoid cache issues
    return hasIt;
  }

  /// Helper method to safely show dialogs after build is complete
  void _showAchievementDialog(String image, String message) {
    Get.dialog(
      AchievementDialog(
        image: image,
        message: message,
      ),
      barrierDismissible: false,
    );
  }

  /// Update the user's achievements list
  Future<void> _updateAchievements(String badgeId) async {
    debugPrint('article _updateAchievements: Adding $badgeId');
    final ProfileController profileController = Get.find();
    var userRef = firestore.collection('users').doc(uid);

    // Get current achievements from profile controller
    List<dynamic> achievements =
        profileController.userr.value.achievements ?? [];
    debugPrint(
        'article _updateAchievements: Current achievements before: $achievements');

    // Add the new badge if it doesn't already exist
    if (!achievements.contains(badgeId)) {
      debugPrint(
          'article _updateAchievements: Achievement not found, adding to Firestore');
      await userRef.update({
        'achievements': FieldValue.arrayUnion([badgeId]),
      });
      // Update local profile controller
      profileController.userr.value.achievements!.add(badgeId);
      // Schedule the UI refresh for after the current build cycle
      WidgetsBinding.instance.addPostFrameCallback((_) {
        profileController.userr.refresh();
      });
      debugPrint(
          'article _updateAchievements: Achievement added locally. New list: ${profileController.userr.value.achievements}');
    } else {
      debugPrint(
          'article _updateAchievements: Achievement already exists, skipping');
    }

    // Check neuron-related achievements (avoid recursive calls)
    int neurons = profileController.userr.value.neurons ?? 0;
    if (neurons > 1000 &&
        !profileController.userr.value.achievements!
            .contains('point_collector')) {
      pointCollector();
    }
    if (neurons > 5000 &&
        !profileController.userr.value.achievements!
            .contains('point_hoarder')) {
      pointHoarder();
    }

    // Check for ultimateLearner achievement (39 total achievements)
    final currentAchievements =
        profileController.userr.value.achievements ?? [];
    if (currentAchievements.length >= 39 &&
        !currentAchievements.contains('ultimate_learner')) {
      ultimateLearner();
    }
  }

  Future<void> _updateNeurons(int neurons) async {
    ProfileController profileController = Get.find();
    final newNeuronTotal =
        neurons + (profileController.userr.value.neurons ?? 0);
    profileController.userr.value.neurons = newNeuronTotal;

    WriteBatch batch = firestore.batch();

    DocumentReference userDoc = firestore.collection('users').doc(uid);

    // Add the updates to the batch
    batch.update(userDoc, {'neurons': FieldValue.increment(neurons)});

    // Commit the batch first
    await batch.commit();

    // Update user title based on new neuron total
    await profileController.userTitleUpdate(newNeuronTotal.toInt());

    // Schedule the UI refresh for after the current build cycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      profileController.userr.refresh();
    });
  }

  Future<void> _updateLeaderboard(int score) async {
    try {
      final leaderboardService = LeaderboardService(uid);
      await leaderboardService.updateWeeklyScore(score);

      // Check for league achievements after updating score
      await _checkLeagueAchievements();
    } catch (e) {
      // Log error but don't throw to prevent breaking the reward flow
      debugPrint('Error updating leaderboard: $e');
    }
  }

  /// Check and award league achievements
  Future<void> _checkLeagueAchievements() async {
    try {
      final profileController = Get.find<ProfileController>();
      final currentLeague = profileController.userr.value.league;

      if (currentLeague == null) return;

      final achievements = profileController.userr.value.achievements ?? [];

      // Map of league achievements - only check for major leagues
      final leagueAchievements = {
        'Srebrna': {'id': 'silver_league_achiever', 'neurons': 500},
        'Zlatna': {'id': 'gold_league_achiever', 'neurons': 1000},
      };

      final achievement = leagueAchievements[currentLeague];
      if (achievement != null && !achievements.contains(achievement['id'])) {
        await _awardLeagueAchievement(
          achievement['id'] as String,
          achievement['neurons'] as int,
          currentLeague,
        );
      }
    } catch (e) {
      debugPrint('Error checking league achievements: $e');
    }
  }

  /// Award a league achievement
  Future<void> _awardLeagueAchievement(
      String achievementId, int neurons, String league) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final profileController = Get.find<ProfileController>();

      // Update user achievements and neurons
      await firestore.collection('users').doc(uid).update({
        'achievements': FieldValue.arrayUnion([achievementId]),
        'neurons': FieldValue.increment(neurons),
      });

      // Update local state
      profileController.userr.value.achievements!.add(achievementId);
      profileController.userr.value.neurons =
          (profileController.userr.value.neurons ?? 0) + neurons;
      profileController.userr.refresh();

      // Show achievement dialog
      _showLeagueAchievementDialog(league, neurons);
    } catch (e) {
      debugPrint('Error awarding league achievement: $e');
    }
  }

  /// Show league achievement dialog
  void _showLeagueAchievementDialog(String league, int neurons) {
    Get.dialog(
      AchievementDialog(
        image: 'assets/images/trophy.svg',
        message:
            'Čestitamo! Dostigli ste $league ligu i osvojili $neurons neurona!',
      ),
      barrierDismissible: false,
    );
  }

  /// Batch all user-related Firestore updates for performance
  Future<void> addNeurons(int neurons, {bool updateLeaderboard = true}) async {
    final profileController = Get.find<ProfileController>();
    final userRef = firestore.collection('users').doc(uid);
    WriteBatch batch = firestore.batch();

    // Update neurons
    batch.update(userRef, {'neurons': FieldValue.increment(neurons)});
    profileController.userr.value.neurons =
        (profileController.userr.value.neurons ?? 0) + neurons;

    await batch.commit();

    // Schedule the UI refresh for after the current build cycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      profileController.userr.refresh();
    });

    // FIXED: Also update leaderboard score
    if (updateLeaderboard) {
      try {
        final leaderboardService = LeaderboardService(uid);
        await leaderboardService.updateWeeklyScore(neurons);
      } catch (e) {
        debugPrint('Error updating leaderboard: $e');
      }
    }
  }

  Future<void> firstSteps() async {
    final ProfileController profileController = Get.find();
    final achievements = profileController.userr.value.achievements ?? [];

    // Check if achievement already earned
    if (achievements.contains('first_steps')) {
      return;
    }

    int neurons = 50;

    // Run all updates in parallel
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('first_steps'),
      firestore.collection('users').doc(uid).update({
        'firstQuizComplete': true,
      }),
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a1', 'Prvi koraci! Završili ste svoj prvi kviz.');
  }

  Future<void> quizWiz() async {
    final ProfileController profileController = Get.find();
    final achievements = profileController.userr.value.achievements ?? [];

    // Check if achievement already earned
    if (achievements.contains('quiz_wiz')) {
      return;
    }

    int neurons = 150;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('quiz_wiz')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a3', 'Majstor kvizova! Završite 10 kvizova.');
  }

  Future<void> topicExplorer() async {
    // Check if achievement already earned
    if (_hasAchievement('topic_explorer')) {
      return;
    }

    int neurons = 50;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('topic_explorer')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a4', 'Istraživač! Otvorili ste svoju prvu temu!');
  }

  Future<void> dailyLearner() async {
    final ProfileController profileController = Get.find();
    final now = Timestamp.now();
    final lastReward = profileController.userr.value.lastDailyLearnerReward;

    // Check if already rewarded this month
    if (lastReward != null) {
      final lastRewardDate = lastReward.toDate();
      final currentDate = now.toDate();

      // Check if it's the same month and year
      if (lastRewardDate.year == currentDate.year &&
          lastRewardDate.month == currentDate.month) {
        return; // Already rewarded this month
      }
    }

    int neurons = 100;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateDailyLearnerTimestamp(now)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a5', 'Upornost! Prijavili ste se 3 uzastopna dana.');
  }

  Future<void> weeklyStreak() async {
    final ProfileController profileController = Get.find();
    final now = Timestamp.now();
    final lastReward = profileController.userr.value.lastWeeklyStreakReward;

    // Check if already rewarded this week
    if (lastReward != null) {
      final lastRewardDate = lastReward.toDate();
      final currentDate = now.toDate();

      // Calculate the start of the current week (Monday)
      final currentWeekStart = _getWeekStart(currentDate);
      final lastRewardWeekStart = _getWeekStart(lastRewardDate);

      // Check if it's the same week
      if (currentWeekStart.isAtSameMomentAs(lastRewardWeekStart)) {
        return; // Already rewarded this week
      }
    }

    int neurons = 200;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateWeeklyStreakTimestamp(now)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a5-1', 'Nedeljni niz! Prijavili ste se 7 uzastopnih dana!');
  }

  Future<void> noviceReader() async {
    // Check if achievement already earned
    final hasAchievement = _hasAchievement('novice_reader');
    debugPrint('article noviceReader: hasAchievement = $hasAchievement');
    if (hasAchievement) {
      debugPrint('article noviceReader: Achievement already earned, returning');
      return;
    }

    debugPrint('article noviceReader: Awarding achievement');
    int neurons = 100;
    try {
      debugPrint('article noviceReader: Starting _updateNeurons');
      await _updateNeurons(neurons);
      debugPrint('article noviceReader: _updateNeurons completed');

      debugPrint('article noviceReader: Starting _updateLeaderboard');
      await _updateLeaderboard(neurons);
      debugPrint('article noviceReader: _updateLeaderboard completed');

      debugPrint('article noviceReader: Starting _updateAchievements');
      await _updateAchievements('novice_reader');
      debugPrint('article noviceReader: _updateAchievements completed');

      debugPrint('article noviceReader: All updates completed successfully');
    } catch (e) {
      debugPrint('article noviceReader: Error during updates: $e');
      debugPrint('article noviceReader: Stack trace: ${StackTrace.current}');
      return; // Don't show dialog if there was an error
    }

    // Show the dialog after updates are completed
    debugPrint('article noviceReader: Showing dialog');
    _showAchievementDialog('a6', 'Početnik čitalac! Pročitali ste 5 članaka.');
  }

  Future<void> intermediateReader() async {
    // Check if achievement already earned
    if (_hasAchievement('intermediate_reader')) {
      return;
    }

    int neurons = 150;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('intermediate_reader')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a7', ' Čitalac! Pročitali ste 10 članaka.');
  }

  Future<void> advancedReader() async {
    // Check if achievement already earned
    if (_hasAchievement('advanced_reader')) {
      return;
    }

    int neurons = 200;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('advanced_reader')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a8', 'Napredni Čitalac! Pročitali ste 20 članaka.');
  }

  Future<void> firstCorrectAnswer() async {
    // Check if achievement already earned
    if (_hasAchievement('first_correct_answer')) {
      return;
    }

    int neurons = 10;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('first_correct_answer')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a9', 'Dobili ste svoj prvi tačan odgovor na kvizu!');
  }

  Future<void> accuracyAce() async {
    // Note: This achievement can be earned multiple times according to requirements
    // "Accuracy Ace: Achieve 90% accuracy in a quiz. – 50 neurons (every time when user achieves this)"
    int neurons = 50;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      // Don't add to achievements list since this can be earned multiple times
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a10', 'As preciznosti! Postigli te 90% tačnosti na kvizu.');
  }

  Future<void> pointCollector() async {
    // Check if achievement already earned
    if (_hasAchievement('point_collector')) {
      return;
    }

    int neurons = 150;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('point_collector')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a11', 'Sakupljač neurona! Uspešno ste sakupili 1000 neurona!');
  }

  Future<void> pointHoarder() async {
    // Check if achievement already earned
    if (_hasAchievement('point_hoarder')) {
      return;
    }

    int neurons = 500;
    await Future.wait([
      _updateNeurons(
        neurons,
      ),
      _updateLeaderboard(neurons),
      _updateAchievements('point_hoarder')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a12', 'Čuvar neurona! Uspešno ste sakupili 5000 neurona!');
  }

  Future<void> silverLeagueAchiever() async {
    // Check if achievement already earned
    if (_hasAchievement('silver_league_achiever')) {
      return;
    }

    int neurons = 500;
    await Future.wait([
      _updateNeurons(
        neurons,
      ),
      _updateLeaderboard(neurons),
      _updateAchievements('silver_league_achiever')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a14', 'Dostigli ste u Srebrnu ligu!');
  }

  Future<void> goldLeagueAchiever() async {
    // Check if achievement already earned
    if (_hasAchievement('gold_league_achiever')) {
      return;
    }

    int neurons = 1000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('gold_league_achiever')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a15', 'Dostigli ste u Zlatnu ligu!');
  }

  Future<void> quizMarathoner() async {
    // Check if achievement already earned
    if (_hasAchievement('quiz_marathoner')) {
      return;
    }

    int neurons = 400;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('quiz_marathoner')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a16', 'Maratonac kvizova! Završili ste 50 kvizova!');
  }

  Future<void> topicEnthusiast() async {
    // Check if achievement already earned
    if (_hasAchievement('topic_enthusiast')) {
      return;
    }

    int neurons = 200;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('topic_enthusiast')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a17', 'Entuzijasta za teme! Pročitali ste 10 različitih tema.');
  }

  Future<void> contentConnoisseur() async {
    // Check if achievement already earned
    if (_hasAchievement('content_connoisseur')) {
      return;
    }

    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('content_connoisseur')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a18', 'Poznavalac sadržaja! Pročitali ste 20 različitih tema.');
  }

  Future<void> streakChampion() async {
    // Check if achievement already earned
    if (_hasAchievement('streak_champion')) {
      return;
    }

    int neurons = 500;
    await Future.wait([
      _updateNeurons(
        neurons,
      ),
      _updateLeaderboard(neurons),
      _updateAchievements('streak_champion')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a19', 'Šampion niza! Održali ste niz od 30 uzastopnih dana.');
  }

  Future<void> classicQuizMaster() async {
    // Check if achievement already earned
    if (_hasAchievement('classic_quiz_master')) {
      return;
    }

    int neurons = 250;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('classic_quiz_master')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a21', 'Majstor kvizova! Završili ste 20 kvizova!');
  }

  Future<void> articleEnthusiast() async {
    // Check if achievement already earned
    if (_hasAchievement('article_enthusiast')) {
      return;
    }

    int neurons = 400;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('article_enthusiast')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a22', 'Entuzijasta za članke! Pročitali ste 50 članaka.');
  }

  Future<void> quizVeteran() async {
    // Check if achievement already earned
    if (_hasAchievement('quiz_veteran')) {
      return;
    }

    int neurons = 600;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('quiz_veteran'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a23', 'Veteran kvizova! Završili ste 100 kvizova.');
  }

  Future<void> learningAddict() async {
    // Check if achievement already earned
    if (_hasAchievement('learning_addict')) {
      return;
    }

    int neurons = 800;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('learning_addict')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a24', 'Ovisnik o učenju: Prijavili ste se uzastopno 60 dana!');
  }

  Future<void> quizWhiz() async {
    // Note: This achievement can be earned multiple times according to requirements
    // "Quiz Whiz: Get 100% on a quiz. – 100 neurons"
    int neurons = 100;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      // Don't add to achievements list since this can be earned multiple times
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a26', 'Kviz guru! Svi 100% tačni odgovori u kvizu!');
  }

  Future<void> historyBuff() async {
    // Check if achievement already earned
    if (_hasAchievement('history_buff')) {
      return;
    }

    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('history_buff')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a27',
        'Ljubitelj storije! Završili te 10 kvizova u kategoriji Istorija.');
  }

  Future<void> mythologyMaster() async {
    // Check if achievement already earned
    if (_hasAchievement('mythology_master')) {
      return;
    }

    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('mythology_master')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a28',
        'Majstor mitologije! Završili te 10 kvizova u kategoriji Mitologija.');
  }

  Future<void> cosmosConqueror() async {
    // Check if achievement already earned
    if (_hasAchievement('cosmos_conqueror')) {
      return;
    }

    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('cosmos_conqueror')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a29',
        'Osvojitelj Kosmosa! Završili te 10 kvizova u kategoriji Kosmos.');
  }

  Future<void> scienceSage() async {
    // Check if achievement already earned
    if (_hasAchievement('science_sage')) {
      return;
    }

    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('science_sage')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a30', 'Mudrac nauke! Završili te 10 kvizova u kategoriji Nauka.');
  }

  Future<void> artAficionado() async {
    // Check if achievement already earned
    if (_hasAchievement('art_aficionado')) {
      return;
    }

    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('art_aficionado'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a31',
        'Aficionado umetnosti! Završili te 10 kvizova u kategoriji Umetnost.');
  }

  Future<void> marathoner() async {
    // Check if achievement already earned
    if (_hasAchievement('marathoner')) {
      return;
    }

    int neurons = 250;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('marathoner'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a32', 'Maratonac! Čitate članke 30 minuta u jednoj sesiji.');
  }

  Future<void> knowledgeSeeker() async {
    // Check if achievement already earned
    if (_hasAchievement('knowledge_seeker')) {
      return;
    }

    int neurons = 800;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('knowledge_seeker')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a33', 'Tragač znanja! Odgovorili ste tačno na 100 pitanja na kvizu.');
  }

  Future<void> quizKingQueen() async {
    // Check if achievement already earned
    if (_hasAchievement('quiz_king_queen')) {
      return;
    }

    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('quiz_king_queen')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a34',
        'Kralj/Kraljica kvizova! Osvojili ste prvo mesto u svojoj ligi.');
  }

  Future<void> participationTrophy() async {
    // Check if achievement already earned
    if (_hasAchievement('participation_trophy')) {
      return;
    }

    int neurons = 1000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('participation_trophy')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a35', 'Trofej za učešće! Učestvujte u 10 ligaških adogađaja.');
  }

  Future<void> competitiveSpirit() async {
    // Check if achievement already earned
    if (_hasAchievement('competitive_spirit')) {
      return;
    }

    int neurons = 500;
    await Future.wait([
      _updateNeurons(
        neurons,
      ),
      _updateLeaderboard(neurons),
      _updateAchievements('competitive_spirit')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a36', 'Takmičarski duh! Osvojili ste 5 promocija u ligama.');
  }

  Future<void> consistentLearner() async {
    // Check if achievement already earned
    if (_hasAchievement('consistent_learner')) {
      return;
    }

    int neurons = 200;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('consistent_learner')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a38',
        'Dosledni učenik! Prijavili ste se i završili bar jedan kviz svakodnevno, 14 dana.');
  }

  Future<void> multiCategoryMaster() async {
    // Check if achievement already earned
    if (_hasAchievement('multi_category_master')) {
      return;
    }

    int neurons = 800;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('multi_category_master'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a39',
        'Majstor u više kategorija! Završili ste kvizove u bar 5 kategorija.');
  }

  Future<void> powerUser() async {
    // Check if achievement already earned
    if (_hasAchievement('power_user')) {
      return;
    }

    int neurons = 1000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('power_user')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a43',
        'Moćni korisnik! Proveli ste više od 1000 minuta u umniLab aplikaciji.');
  }

  Future<void> perfectionist() async {
    // Check if achievement already earned
    if (_hasAchievement('perfectionist')) {
      return;
    }

    int neurons = 250;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('perfectionist')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a47',
        'Perfekcionista! Tačno ste odgovorili na sva pitanja u 5 uzastopnih kvizova.');
  }

  Future<void> loyalUser() async {
    // Check if achievement already earned
    if (_hasAchievement('loyal_user')) {
      return;
    }

    int neurons = 1000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('loyal_user')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a49', 'Verni korisnik! Održali ste niz od 90 dana.');
  }

  Future<void> ultimateLearner() async {
    // Check if achievement already earned
    if (_hasAchievement('ultimate_learner')) {
      return;
    }

    int neurons = 3000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('ultimate_learner'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('50',
        'Ultimativni naučnik! Osvojili ste svaki bedž u umniLab platformi!');
  }

  /// Helper method to update Daily Learner timestamp
  Future<void> _updateDailyLearnerTimestamp(Timestamp timestamp) async {
    final ProfileController profileController = Get.find();
    var userRef = firestore.collection('users').doc(uid);

    await userRef.update({
      'lastDailyLearnerReward': timestamp,
    });

    // Update local profile controller
    profileController.userr.value.lastDailyLearnerReward = timestamp;

    // Schedule the UI refresh for after the current build cycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      profileController.userr.refresh();
    });
  }

  /// Helper method to update Weekly Streak timestamp
  Future<void> _updateWeeklyStreakTimestamp(Timestamp timestamp) async {
    final ProfileController profileController = Get.find();
    var userRef = firestore.collection('users').doc(uid);

    await userRef.update({
      'lastWeeklyStreakReward': timestamp,
    });

    // Update local profile controller
    profileController.userr.value.lastWeeklyStreakReward = timestamp;

    // Schedule the UI refresh for after the current build cycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      profileController.userr.refresh();
    });
  }

  /// Helper method to get the start of the week (Monday)
  DateTime _getWeekStart(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return DateTime(date.year, date.month, date.day - daysFromMonday);
  }
}
