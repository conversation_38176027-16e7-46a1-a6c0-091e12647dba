import 'dart:io';
import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/box_widget.dart';
import 'package:bibl/widgets/premium_skeleton_loader.dart';
import 'package:bibl/utils/premium_animations.dart';
import 'package:bibl/utils/lightning_image_preloader.dart';
import 'interests_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

/// Optimized MergedItemsList with memory leak fixes and premium performance
class OptimizedMergedItemsList extends StatefulWidget {
  final bool isForLibrary;
  final ScrollController scrollController;

  const OptimizedMergedItemsList({
    Key? key,
    required this.isForLibrary,
    required this.scrollController,
  }) : super(key: key);

  @override
  State<OptimizedMergedItemsList> createState() =>
      _OptimizedMergedItemsListState();
}

class _OptimizedMergedItemsListState extends State<OptimizedMergedItemsList>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();

  // Optimized animation and ad management
  final Map<int, BannerAd> _activeBannerAds = {};
  final Set<int> _loadedAdIndices = {};

  // FAB and scroll management
  bool _showScrollToTopButton = false;
  bool _isLoadingMore = false;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;

  // Performance optimizations
  static const int _maxConcurrentAds = 3; // Limit concurrent ads
  final Set<int> _visibleIndices = {};

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupScrollListener();

    // Preload first few ads immediately for smooth experience
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!(profileController.userr.value.isPremiumUser ?? false)) {
        _preloadInitialAds();
      }
    });
  }

  void _preloadInitialAds() {
    // Preload first 3 ads immediately
    for (int i = 3; i <= 9; i += 3) {
      if (_activeBannerAds.length < _maxConcurrentAds) {
        _loadBannerAd(i);
      }
    }
  }

  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  void _setupScrollListener() {
    widget.scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    final currentOffset = widget.scrollController.offset;
    final maxScrollExtent = widget.scrollController.position.maxScrollExtent;

    // Show/hide FAB based on scroll position
    final shouldShowFAB = currentOffset > 200;
    if (shouldShowFAB != _showScrollToTopButton) {
      setState(() {
        _showScrollToTopButton = shouldShowFAB;
      });

      if (shouldShowFAB) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    }

    // Intelligent image preloading based on scroll position
    _preloadImagesAhead();

    // Load more content when near bottom (more responsive trigger)
    if (maxScrollExtent > 0 &&
        currentOffset >= maxScrollExtent - 800 &&
        !_isLoadingMore) {
      _loadMoreContent();
    }

    // Update visible indices for ad management
    _updateVisibleIndices(currentOffset);
  }

  void _updateVisibleIndices(double scrollOffset) {
    if (!mounted) return;

    const itemHeight = 220.0; // Approximate item height
    final viewportHeight = MediaQuery.of(context).size.height;

    final firstVisibleIndex = (scrollOffset / itemHeight).floor();
    final lastVisibleIndex =
        ((scrollOffset + viewportHeight) / itemHeight).ceil();

    final newVisibleIndices = <int>{};
    for (int i = firstVisibleIndex; i <= lastVisibleIndex; i++) {
      if (i >= 0 && i < _getItemCount()) {
        newVisibleIndices.add(i);
      }
    }

    // Clean up ads that are no longer visible
    _cleanupInvisibleAds(newVisibleIndices);

    _visibleIndices.clear();
    _visibleIndices.addAll(newVisibleIndices);
  }

  void _preloadImagesAhead() {
    if (!mounted) return;

    try {
      final currentItems = _getItems();
      if (currentItems.isEmpty) return;

      // Get currently visible items and next batch for preloading
      final visibleUrls = <String>[];
      final nextUrls = <String>[];

      for (final index in _visibleIndices) {
        if (index < currentItems.length) {
          final item = currentItems[index];
          final url = _getImageUrl(item);
          if (url != null) visibleUrls.add(url);
        }
      }

      // Preload next 10 items ahead
      final maxVisibleIndex = _visibleIndices.isNotEmpty
          ? _visibleIndices.reduce((a, b) => a > b ? a : b)
          : 0;
      for (int i = maxVisibleIndex + 1;
          i < currentItems.length && i <= maxVisibleIndex + 10;
          i++) {
        final item = currentItems[i];
        final url = _getImageUrl(item);
        if (url != null) nextUrls.add(url);
      }

      // Smart preload with priority for visible items
      LightningImagePreloader.smartPreload(visibleUrls, nextUrls);
    } catch (e) {
      debugPrint('Error in image preloading: $e');
    }
  }

  String? _getImageUrl(dynamic item) {
    if (item is LessonModel) {
      return item.imageLink;
    } else if (item is QuizModel) {
      return item.quizImageLink;
    }
    return null;
  }

  void _cleanupInvisibleAds(Set<int> newVisibleIndices) {
    final adsToRemove = <int>[];

    for (final index in _activeBannerAds.keys) {
      if (!newVisibleIndices.contains(index)) {
        adsToRemove.add(index);
      }
    }

    for (final index in adsToRemove) {
      _activeBannerAds[index]?.dispose();
      _activeBannerAds.remove(index);
      _loadedAdIndices.remove(index);
    }
  }

  Future<void> _loadMoreContent() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Load more items using the lesson controller
      if (widget.isForLibrary) {
        lessonController.loadMoreLibraryDisplayItems();
      } else {
        lessonController.loadMoreHomeDisplayItems();
      }

      // Preload ads for upcoming positions if not premium
      if (!(profileController.userr.value.isPremiumUser ?? false)) {
        final currentLength = widget.isForLibrary
            ? lessonController.libraryDisplayedItems.length
            : lessonController.homeDisplayedItems.length;

        // Preload next 2 ads that will be needed
        for (int i = 1; i <= 2; i++) {
          final nextAdIndex = ((currentLength + i) ~/ 3) + 3;
          if (!_activeBannerAds.containsKey(nextAdIndex) &&
              !_loadedAdIndices.contains(nextAdIndex) &&
              _activeBannerAds.length < _maxConcurrentAds) {
            _loadBannerAd(nextAdIndex);
          }
        }
      }

      await Future.delayed(const Duration(milliseconds: 300));
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  int _getItemCount() {
    return widget.isForLibrary
        ? lessonController.libraryDisplayedItems.length
        : lessonController.homeDisplayedItems.length;
  }

  List<dynamic> _getItems() {
    return widget.isForLibrary
        ? lessonController.libraryDisplayedItems
        : lessonController.homeDisplayedItems;
  }

  bool _isAdIndex(int index) {
    // Show ad after every 3rd item (so at positions 3, 7, 11, etc.)
    return (index + 1) % 4 == 0;
  }

  int _calculateAdIndex(int visualIndex) {
    // Calculate the actual ad index based on visual position
    return (visualIndex ~/ 4) + 3;
  }

  int _calculateItemIndex(int visualIndex, bool isPremiumUser) {
    if (isPremiumUser) return visualIndex;

    int itemIndex = 0;
    for (int i = 0; i <= visualIndex; i++) {
      if (!_isAdIndex(i)) {
        if (i == visualIndex) return itemIndex;
        itemIndex++;
      }
    }
    return itemIndex;
  }

  int _countAds(int itemCount) {
    int adCount = 0;
    for (int i = 0; i < itemCount + adCount; i++) {
      if (_isAdIndex(i)) adCount++;
    }
    return adCount;
  }

  int _getTotalChildCount() {
    final isPremiumUser = profileController.userr.value.isPremiumUser ?? false;
    final itemCount = _getItemCount();
    return itemCount + (isPremiumUser ? 0 : _countAds(itemCount));
  }

  Widget _buildOptimizedAd(int adIndex) {
    // Always return a fixed-size container to prevent layout jumps
    const double fixedAdHeight = 340.0;

    return RepaintBoundary(
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 10, 16, 20),
        height: fixedAdHeight, // Fixed height prevents layout jumps
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // Ad label
            Padding(
              padding: const EdgeInsets.only(top: 8, bottom: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'REKLAMA',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Ad content area
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildAdContent(adIndex),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdContent(int adIndex) {
    // Check if ad is loaded
    final ad = _activeBannerAds[adIndex];
    final isLoaded = ad != null && _loadedAdIndices.contains(adIndex);

    if (isLoaded) {
      return FadeTransition(
        opacity: const AlwaysStoppedAnimation(1.0),
        child: AdWidget(ad: ad),
      );
    }

    // Show loading state or start loading if not started
    if (!_loadedAdIndices.contains(adIndex) &&
        _activeBannerAds.length < _maxConcurrentAds) {
      _loadBannerAd(adIndex);
    }

    // Return simplified loading placeholder to prevent layout shifts
    return Container(
      width: double.infinity,
      color: Colors.grey[100],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2.0,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
            ),
          ),
          const SizedBox(height: 12),
        ],
      ),
    );
  }

  Future<void> _loadBannerAd(int index) async {
    if (_loadedAdIndices.contains(index) ||
        _activeBannerAds.containsKey(index)) {
      return;
    }

    _loadedAdIndices.add(index);

    try {
      // Use production ad units or test units based on index
      final adUnitMap = Platform.isAndroid
          ? {
              3: 'ca-app-pub-8639821055582439/1096152024',
              6: 'ca-app-pub-8639821055582439/9477204925',
              9: 'ca-app-pub-8639821055582439/4156563660',
              -1: 'ca-app-pub-8639821055582439/8056296139',
            }
          : {
              3: 'ca-app-pub-8639821055582439/3769550858',
              6: 'ca-app-pub-8639821055582439/4265224933',
              9: 'ca-app-pub-8639821055582439/1746888714',
              -1: 'ca-app-pub-8639821055582439/2021354915',
            };

      final adUnitId = adUnitMap[index] ?? adUnitMap[-1]!;

      final ad = BannerAd(
        adUnitId: adUnitId,
        size: AdSize(
          width: (Get.width - 50).toInt(),
          height: 280, // Slightly smaller for better fit
        ),
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            if (mounted) {
              // Update without setState to prevent rebuilds during scrolling
              _activeBannerAds[index] = ad as BannerAd;
              // Only setState if this ad is currently visible
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  setState(() {});
                }
              });
            } else {
              ad.dispose();
            }
          },
          onAdFailedToLoad: (ad, error) {
            debugPrint('Ad failed to load at index $index: ${error.message}');
            ad.dispose();
            _loadedAdIndices.remove(index);
            if (mounted) {
              setState(() {
                _activeBannerAds.remove(index);
              });
            }
          },
        ),
      );

      // Load ad asynchronously to prevent blocking
      ad.load();
    } catch (e) {
      debugPrint('Error loading ad at index $index: $e');
      _loadedAdIndices.remove(index);
    }
  }

  Widget _buildOptimizedItem(int index) {
    final items = _getItems();
    if (index >= items.length) {
      return const PremiumSkeletonLoader.card();
    }

    final item = items[index];

    return RepaintBoundary(
      key: ValueKey('item_$index'),
      child: PremiumAnimations.fadeSlideIn(
        duration: const Duration(milliseconds: 400),
        delay: Duration(milliseconds: (index % 5) * 50),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: BoxWidget(
            lesson: item is LessonModel ? item : null,
            quiz: item is QuizModel ? item : null,
            shuffleQuiz: item is ShuffleQuizModel ? item : null,
          ),
        ),
      ),
    );
  }

  void _scrollToTop() {
    widget.scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeOutCubic,
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Obx(() {
      final items = _getItems();
      if (items.isEmpty) {
        if (profileController.isUserDataLoading.value) {
          return const PremiumSkeletonLoader.list(itemCount: 5);
        }
        return _buildEmptyState();
      }

      return Stack(
        children: [
          // Main content with CustomScrollView
          RefreshIndicator(
            color: mainColor,
            onRefresh: _onRefresh,
            child: CustomScrollView(
              controller: widget.scrollController,
              physics: const BouncingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics(),
              ),
              slivers: [
                // Interests header
                SliverToBoxAdapter(
                  child: PremiumAnimations.fadeSlideIn(
                    duration: const Duration(milliseconds: 600),
                    child: Column(
                      children: [
                        interestsWidget(context, widget.isForLibrary),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),

                // Main content list
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final isPremiumUser =
                          profileController.userr.value.isPremiumUser ?? false;

                      // Show ad logic
                      if (!isPremiumUser && _isAdIndex(index)) {
                        return _buildOptimizedAd(_calculateAdIndex(index));
                      }

                      final itemIndex =
                          _calculateItemIndex(index, isPremiumUser);
                      if (itemIndex < 0 || itemIndex >= _getItemCount()) {
                        return const SizedBox.shrink();
                      }

                      return _buildOptimizedItem(itemIndex);
                    },
                    childCount: _getTotalChildCount(),
                  ),
                ),

                // Loading indicator at bottom
                if (_isLoadingMore)
                  const SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: Center(
                        child: PremiumLoadingIndicator(),
                      ),
                    ),
                  ),

                // Bottom padding for FAB
                const SliverPadding(
                  padding: EdgeInsets.only(bottom: 100),
                ),
              ],
            ),
          ),

          // Premium FAB
          if (_showScrollToTopButton)
            Positioned(
              right: 16,
              bottom: 16,
              child: ScaleTransition(
                scale: _fabScaleAnimation,
                child: PremiumFloatingActionButton(
                  onPressed: _scrollToTop,
                  backgroundColor: mainColor,
                  foregroundColor: Colors.white,
                  child: const Icon(
                    Icons.keyboard_arrow_up,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ),
        ],
      );
    });
  }

  Widget _buildEmptyState() {
    return Center(
      child: PremiumAnimations.fadeSlideIn(
        duration: const Duration(milliseconds: 600),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.library_books_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              widget.isForLibrary
                  ? 'Nema sadržaja u biblioteci'
                  : 'Nema sadržaja',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Povucite da osvežite',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onRefresh() async {
    if (widget.isForLibrary) {
      lessonController.shuffleAllItems(
        isShuffle: true,
        shouldClear: true,
        from: 'optimized list library refresh',
      );
    } else {
      lessonController.mergeAndShuffleItems(
        isShuffle: true,
        from: 'optimized list home refresh',
        shouldClear: true,
      );
    }

    // Small delay for smooth UX
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    // Critical: Properly dispose all resources
    widget.scrollController.removeListener(_onScroll);
    _fabAnimationController.dispose();

    // Dispose all active ads
    for (final ad in _activeBannerAds.values) {
      ad.dispose();
    }
    _activeBannerAds.clear();

    // Clear all tracking sets
    _loadedAdIndices.clear();
    _visibleIndices.clear();

    super.dispose();
  }
}
